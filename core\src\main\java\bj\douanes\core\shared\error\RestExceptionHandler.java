package bj.douanes.core.shared.error;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.InsufficientAuthenticationException;
import org.springframework.security.core.AuthenticationException;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.servlet.NoHandlerFoundException;

import bj.douanes.core.shared.dto.FieldDto;
import bj.douanes.core.shared.dto.AppErrorDto;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.MalformedJwtException;
import io.jsonwebtoken.security.SignatureException;
import jakarta.persistence.EntityNotFoundException;
import jakarta.persistence.OptimisticLockException;
import jakarta.persistence.PersistenceException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolationException;

import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

import static org.springframework.web.context.request.RequestAttributes.SCOPE_REQUEST;
import static org.springframework.web.servlet.HandlerMapping.PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE;

@Slf4j
@RestControllerAdvice
public class RestExceptionHandler {

    @Value("${spring.profiles.active:dev}")
    private String activeProfile;

    @Value("${app.error.include-stacktrace:false}")
    private boolean includeStackTrace;

    @Value("${app.error.include-binding-errors:true}")
    private boolean includeBindingErrors;

    private static final String PROD_ERROR_MESSAGE = "Une erreur interne s'est produite";
    private static final String ERROR_ID_HEADER = "X-Error-ID";
    private static final DateTimeFormatter TIMESTAMP_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * Génère un ID unique pour tracer l'erreur
     */
    private String generateErrorId() {
        return UUID.randomUUID().toString().substring(0, 8);
    }

    /**
     * Détermine si on est en environnement de production
     */
    private boolean isProduction() {
        return "prod".equals(activeProfile) || "production".equals(activeProfile);
    }

    /**
     * Crée une réponse d'erreur standardisée
     */
    private AppErrorDto createErrorResponse(String message, HttpStatus status, String path, String errorId) {
        AppErrorDto appError = new AppErrorDto();
        appError.setError(isProduction() ? PROD_ERROR_MESSAGE : message);
        appError.setStatusCode(status);
        appError.setMessage(isProduction() ? "Contactez l'administrateur avec l'ID: " + errorId : message);
        return appError;
    }

    /**
     * Log l'erreur avec toutes les informations contextuelles
     */
    private void logError(String errorId, HttpStatus status, String message, String path,
                         Throwable throwable, HttpServletRequest request) {
        String userAgent = request != null ? request.getHeader("User-Agent") : "Unknown";
        String remoteAddr = request != null ? request.getRemoteAddr() : "Unknown";

        log.error("ERROR_ID: {} | STATUS: {} | MESSAGE: {} | PATH: {} | USER_AGENT: {} | IP: {} | TIMESTAMP: {}",
                errorId, status, message, path, userAgent, remoteAddr,
                LocalDateTime.now().format(TIMESTAMP_FORMATTER), throwable);
    }

    // ==================== VALIDATION EXCEPTIONS ====================

    /**
     * Gestion des erreurs de validation des arguments de méthode
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<AppErrorDto> handleValidationException(WebRequest webRequest,
                    MethodArgumentNotValidException ex, HttpServletRequest request) {

        String errorId = generateErrorId();
        String path = (String) webRequest.getAttribute(PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE, SCOPE_REQUEST);
        final HttpStatus status = HttpStatus.BAD_REQUEST;
        final String message = "Erreurs de validation des données";

        // Construction des erreurs de champs
        final List<FieldDto> fields = ex.getBindingResult().getFieldErrors()
                        .stream()
                        .map(fe -> new FieldDto(
                                        fe.getDefaultMessage(),
                                        fe.getField(),
                                        fe.getRejectedValue(),
                                        fe.getCode()))
                        .toList();

        // Ajout des erreurs globales si présentes
        List<FieldDto> allErrors = new ArrayList<>(fields);
        ex.getBindingResult().getGlobalErrors().forEach(ge ->
            allErrors.add(new FieldDto(ge.getDefaultMessage(), "global", null, ge.getCode()))
        );

        AppErrorDto appError = createErrorResponse(message, status, path, errorId);
        if (includeBindingErrors) {
            appError.setFieldErrors(allErrors);
        }

        logError(errorId, status, message, path, ex, request);

        return ResponseEntity.status(status)
                .header(ERROR_ID_HEADER, errorId)
                .body(appError);
    }

    /**
     * Gestion des violations de contraintes de validation
     */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<AppErrorDto> handleConstraintViolationException(WebRequest webRequest,
                    ConstraintViolationException ex, HttpServletRequest request) {

        String errorId = generateErrorId();
        String path = (String) webRequest.getAttribute(PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE, SCOPE_REQUEST);
        final HttpStatus status = HttpStatus.BAD_REQUEST;
        final String message = "Violation des contraintes de validation";

        List<FieldDto> fields = ex.getConstraintViolations()
                .stream()
                .map(cv -> new FieldDto(
                        cv.getMessage(),
                        cv.getPropertyPath().toString(),
                        cv.getInvalidValue(),
                        cv.getConstraintDescriptor().getAnnotation().annotationType().getSimpleName()))
                .toList();

        AppErrorDto appError = createErrorResponse(message, status, path, errorId);
        if (includeBindingErrors) {
            appError.setFieldErrors(fields);
        }

        logError(errorId, status, message, path, ex, request);

        return ResponseEntity.status(status)
                .header(ERROR_ID_HEADER, errorId)
                .body(appError);
    }

    // ==================== BUSINESS EXCEPTIONS ====================

    /**
     * Gestion des exceptions métier personnalisées
     */
    @ExceptionHandler(ApiException.class)
    public ResponseEntity<AppErrorDto> handleApiException(WebRequest webRequest,
                    ApiException ex, HttpServletRequest request) {

        String errorId = generateErrorId();
        String path = (String) webRequest.getAttribute(PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE, SCOPE_REQUEST);
        final HttpStatus status = ex.getStatus();
        final String message = (!ex.getMessage().isBlank()) ? ex.getMessage()
                        : "Erreur métier non spécifiée";

        AppErrorDto appError = createErrorResponse(message, status, path, errorId);

        Throwable throwable = ex.getThrowable() != null ? ex.getThrowable() : ex;
        logError(errorId, status, message, path, throwable, request);

        return ResponseEntity.status(status)
                .header(ERROR_ID_HEADER, errorId)
                .body(appError);
    }

    // ==================== DATABASE EXCEPTIONS ====================

    /**
     * Gestion des violations d'intégrité de données
     */
    @ExceptionHandler(DataIntegrityViolationException.class)
    @ResponseStatus(HttpStatus.CONFLICT)
    public ResponseEntity<AppErrorDto> handleDataIntegrityViolationException(WebRequest webRequest,
                    DataIntegrityViolationException ex, HttpServletRequest request) {

        String errorId = generateErrorId();
        String path = (String) webRequest.getAttribute(PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE, SCOPE_REQUEST);
        final HttpStatus status = HttpStatus.CONFLICT;

        String message = "Violation de contrainte de données";
        if (!isProduction() && ex.getCause() instanceof SQLException sqlEx) {
            message = switch (sqlEx.getErrorCode()) {
                case 1062 -> "Données en doublon détectées";
                case 1451 -> "Impossible de supprimer : données référencées";
                case 1452 -> "Référence invalide vers des données inexistantes";
                default -> "Erreur d'intégrité des données: " + sqlEx.getMessage();
            };
        }

        AppErrorDto appError = createErrorResponse(message, status, path, errorId);
        logError(errorId, status, message, path, ex, request);

        return ResponseEntity.status(status)
                .header(ERROR_ID_HEADER, errorId)
                .body(appError);
    }

    /**
     * Gestion des entités non trouvées
     */
    @ExceptionHandler(EntityNotFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public ResponseEntity<AppErrorDto> handleEntityNotFoundException(WebRequest webRequest,
                    EntityNotFoundException ex, HttpServletRequest request) {

        String errorId = generateErrorId();
        String path = (String) webRequest.getAttribute(PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE, SCOPE_REQUEST);
        final HttpStatus status = HttpStatus.NOT_FOUND;
        final String message = "Ressource non trouvée";

        AppErrorDto appError = createErrorResponse(message, status, path, errorId);
        logError(errorId, status, message, path, ex, request);

        return ResponseEntity.status(status)
                .header(ERROR_ID_HEADER, errorId)
                .body(appError);
    }

    /**
     * Gestion des conflits de verrouillage optimiste
     */
    @ExceptionHandler(OptimisticLockException.class)
    @ResponseStatus(HttpStatus.CONFLICT)
    public ResponseEntity<AppErrorDto> handleOptimisticLockException(WebRequest webRequest,
                    OptimisticLockException ex, HttpServletRequest request) {

        String errorId = generateErrorId();
        String path = (String) webRequest.getAttribute(PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE, SCOPE_REQUEST);
        final HttpStatus status = HttpStatus.CONFLICT;
        final String message = "Conflit de modification simultanée. Veuillez recharger et réessayer.";

        AppErrorDto appError = createErrorResponse(message, status, path, errorId);
        logError(errorId, status, message, path, ex, request);

        return ResponseEntity.status(status)
                .header(ERROR_ID_HEADER, errorId)
                .body(appError);
    }

    // ==================== SECURITY EXCEPTIONS ====================

    /**
     * Gestion des erreurs d'authentification
     */
    @ExceptionHandler({BadCredentialsException.class, AuthenticationException.class})
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    public ResponseEntity<AppErrorDto> handleAuthenticationException(WebRequest webRequest,
                    AuthenticationException ex, HttpServletRequest request) {

        String errorId = generateErrorId();
        String path = (String) webRequest.getAttribute(PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE, SCOPE_REQUEST);
        final HttpStatus status = HttpStatus.UNAUTHORIZED;
        final String message = "Identifiants invalides";

        AppErrorDto appError = createErrorResponse(message, status, path, errorId);
        logError(errorId, status, message, path, ex, request);

        return ResponseEntity.status(status)
                .header(ERROR_ID_HEADER, errorId)
                .body(appError);
    }

    /**
     * Gestion des erreurs d'autorisation
     */
    @ExceptionHandler(AccessDeniedException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public ResponseEntity<AppErrorDto> handleAccessDeniedException(WebRequest webRequest,
                    AccessDeniedException ex, HttpServletRequest request) {

        String errorId = generateErrorId();
        String path = (String) webRequest.getAttribute(PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE, SCOPE_REQUEST);
        final HttpStatus status = HttpStatus.FORBIDDEN;
        final String message = "Accès refusé : permissions insuffisantes";

        AppErrorDto appError = createErrorResponse(message, status, path, errorId);
        logError(errorId, status, message, path, ex, request);

        return ResponseEntity.status(status)
                .header(ERROR_ID_HEADER, errorId)
                .body(appError);
    }

    /**
     * Gestion des erreurs JWT
     */
    @ExceptionHandler({ExpiredJwtException.class, MalformedJwtException.class, SignatureException.class})
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    public ResponseEntity<AppErrorDto> handleJwtException(WebRequest webRequest,
                    Exception ex, HttpServletRequest request) {

        String errorId = generateErrorId();
        String path = (String) webRequest.getAttribute(PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE, SCOPE_REQUEST);
        final HttpStatus status = HttpStatus.UNAUTHORIZED;

        String message;
        if (ex instanceof ExpiredJwtException) {
            message = "Token expiré";
        } else if (ex instanceof MalformedJwtException) {
            message = "Token malformé";
        } else if (ex instanceof SignatureException) {
            message = "Signature du token invalide";
        } else {
            message = "Token invalide";
        }

        AppErrorDto appError = createErrorResponse(message, status, path, errorId);
        logError(errorId, status, message, path, ex, request);

        return ResponseEntity.status(status)
                .header(ERROR_ID_HEADER, errorId)
                .body(appError);
    }

    // ==================== HTTP EXCEPTIONS ====================

    /**
     * Gestion des méthodes HTTP non supportées
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    @ResponseStatus(HttpStatus.METHOD_NOT_ALLOWED)
    public ResponseEntity<AppErrorDto> handleMethodNotSupportedException(WebRequest webRequest,
                    HttpRequestMethodNotSupportedException ex, HttpServletRequest request) {

        String errorId = generateErrorId();
        String path = (String) webRequest.getAttribute(PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE, SCOPE_REQUEST);
        final HttpStatus status = HttpStatus.METHOD_NOT_ALLOWED;
        final String message = String.format("Méthode %s non supportée. Méthodes autorisées: %s",
                ex.getMethod(), Arrays.toString(ex.getSupportedMethods()));

        AppErrorDto appError = createErrorResponse(message, status, path, errorId);
        logError(errorId, status, message, path, ex, request);

        return ResponseEntity.status(status)
                .header(ERROR_ID_HEADER, errorId)
                .header("Allow", String.join(", ", ex.getSupportedMethods()))
                .body(appError);
    }

    /**
     * Gestion des types de média non supportés
     */
    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    @ResponseStatus(HttpStatus.UNSUPPORTED_MEDIA_TYPE)
    public ResponseEntity<AppErrorDto> handleMediaTypeNotSupportedException(WebRequest webRequest,
                    HttpMediaTypeNotSupportedException ex, HttpServletRequest request) {

        String errorId = generateErrorId();
        String path = (String) webRequest.getAttribute(PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE, SCOPE_REQUEST);
        final HttpStatus status = HttpStatus.UNSUPPORTED_MEDIA_TYPE;
        final String message = String.format("Type de média %s non supporté. Types supportés: %s",
                ex.getContentType(), ex.getSupportedMediaTypes());

        AppErrorDto appError = createErrorResponse(message, status, path, errorId);
        logError(errorId, status, message, path, ex, request);

        return ResponseEntity.status(status)
                .header(ERROR_ID_HEADER, errorId)
                .body(appError);
    }
    /**
     * Gestion des paramètres de requête manquants
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<AppErrorDto> handleMissingParameterException(WebRequest webRequest,
                    MissingServletRequestParameterException ex, HttpServletRequest request) {

        String errorId = generateErrorId();
        String path = (String) webRequest.getAttribute(PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE, SCOPE_REQUEST);
        final HttpStatus status = HttpStatus.BAD_REQUEST;
        final String message = String.format("Paramètre requis manquant: %s (type: %s)",
                ex.getParameterName(), ex.getParameterType());

        AppErrorDto appError = createErrorResponse(message, status, path, errorId);
        logError(errorId, status, message, path, ex, request);

        return ResponseEntity.status(status)
                .header(ERROR_ID_HEADER, errorId)
                .body(appError);
    }

    /**
     * Gestion des erreurs de conversion de type d'argument
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<AppErrorDto> handleTypeMismatchException(WebRequest webRequest,
                    MethodArgumentTypeMismatchException ex, HttpServletRequest request) {

        String errorId = generateErrorId();
        String path = (String) webRequest.getAttribute(PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE, SCOPE_REQUEST);
        final HttpStatus status = HttpStatus.BAD_REQUEST;
        final String message = String.format("Type invalide pour le paramètre '%s': attendu %s, reçu '%s'",
                ex.getName(),
                ex.getRequiredType() != null ? ex.getRequiredType().getSimpleName() : "inconnu",
                ex.getValue());

        AppErrorDto appError = createErrorResponse(message, status, path, errorId);
        logError(errorId, status, message, path, ex, request);

        return ResponseEntity.status(status)
                .header(ERROR_ID_HEADER, errorId)
                .body(appError);
    }

    /**
     * Gestion des erreurs de lecture de message HTTP
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<AppErrorDto> handleMessageNotReadableException(WebRequest webRequest,
                    HttpMessageNotReadableException ex, HttpServletRequest request) {

        String errorId = generateErrorId();
        String path = (String) webRequest.getAttribute(PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE, SCOPE_REQUEST);
        final HttpStatus status = HttpStatus.BAD_REQUEST;
        String message = "Format de données invalide";

        if (!isProduction() && ex.getCause() != null) {
            message = "Erreur de format JSON: " + ex.getMostSpecificCause().getMessage();
        }

        AppErrorDto appError = createErrorResponse(message, status, path, errorId);
        logError(errorId, status, message, path, ex, request);

        return ResponseEntity.status(status)
                .header(ERROR_ID_HEADER, errorId)
                .body(appError);
    }

    /**
     * Gestion des erreurs de taille de fichier dépassée
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    @ResponseStatus(HttpStatus.PAYLOAD_TOO_LARGE)
    public ResponseEntity<AppErrorDto> handleMaxUploadSizeExceededException(WebRequest webRequest,
                    MaxUploadSizeExceededException ex, HttpServletRequest request) {

        String errorId = generateErrorId();
        String path = (String) webRequest.getAttribute(PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE, SCOPE_REQUEST);
        final HttpStatus status = HttpStatus.PAYLOAD_TOO_LARGE;
        final String message = String.format("Taille de fichier dépassée. Maximum autorisé: %d bytes",
                ex.getMaxUploadSize());

        AppErrorDto appError = createErrorResponse(message, status, path, errorId);
        logError(errorId, status, message, path, ex, request);

        return ResponseEntity.status(status)
                .header(ERROR_ID_HEADER, errorId)
                .body(appError);
    }

    /**
     * Gestion des erreurs 404 - Page non trouvée
     */
    @ExceptionHandler(NoHandlerFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public ResponseEntity<AppErrorDto> handleNoHandlerFoundException(WebRequest webRequest,
                    NoHandlerFoundException ex, HttpServletRequest request) {

        String errorId = generateErrorId();
        String path = (String) webRequest.getAttribute(PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE, SCOPE_REQUEST);
        final HttpStatus status = HttpStatus.NOT_FOUND;
        final String message = String.format("Endpoint non trouvé: %s %s", ex.getHttpMethod(), ex.getRequestURL());

        AppErrorDto appError = createErrorResponse(message, status, path, errorId);
        logError(errorId, status, message, path, ex, request);

        return ResponseEntity.status(status)
                .header(ERROR_ID_HEADER, errorId)
                .body(appError);
    }

    // ==================== GENERAL EXCEPTION HANDLER ====================

    /**
     * Gestionnaire général pour toutes les autres exceptions non gérées
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<AppErrorDto> handleAllOtherException(WebRequest webRequest,
                    Exception ex, HttpServletRequest request) {

        String errorId = generateErrorId();
        String path = (String) webRequest.getAttribute(PATH_WITHIN_HANDLER_MAPPING_ATTRIBUTE, SCOPE_REQUEST);
        HttpStatus status = HttpStatus.INTERNAL_SERVER_ERROR;
        String message = PROD_ERROR_MESSAGE;

        // Gestion spécifique pour certaines exceptions non couvertes
        if (ex instanceof IllegalArgumentException) {
            status = HttpStatus.BAD_REQUEST;
            message = isProduction() ? "Argument invalide" : ex.getMessage();
        } else if (ex instanceof IllegalStateException) {
            status = HttpStatus.CONFLICT;
            message = isProduction() ? "État invalide" : ex.getMessage();
        } else if (ex instanceof UnsupportedOperationException) {
            status = HttpStatus.NOT_IMPLEMENTED;
            message = "Opération non supportée";
        }

        AppErrorDto appError = createErrorResponse(message, status, path, errorId);

        // Log avec niveau ERROR pour les erreurs 5xx, WARN pour les autres
        if (status.is5xxServerError()) {
            logError(errorId, status, message, path, ex, request);
        } else {
            log.warn("ERROR_ID: {} | STATUS: {} | MESSAGE: {} | PATH: {}",
                    errorId, status, message, path);
        }

        return ResponseEntity.status(status)
                .header(ERROR_ID_HEADER, errorId)
                .body(appError);
    }
}
