# Configuration avancée de gestion d'erreurs pour RestExceptionHandler

# ========================================
# CONFIGURATION GÉNÉRALE DES ERREURS
# ========================================

# Inclure la stack trace dans les réponses d'erreur (dev/test uniquement)
app.error.include-stacktrace=false

# Inclure les détails des erreurs de validation dans les réponses
app.error.include-binding-errors=true

# Niveau de log pour les erreurs non critiques
app.error.log-level=WARN

# ========================================
# CONFIGURATION SPRING BOOT
# ========================================

# Désactiver la page d'erreur par défaut de Spring Boot
server.error.whitelabel.enabled=false

# Inclure le message d'erreur dans la réponse
server.error.include-message=always

# Inclure les détails de l'exception (dev uniquement)
server.error.include-exception=false

# Inclure la stack trace (dev uniquement)
server.error.include-stacktrace=never

# Inclure les erreurs de binding (validation)
server.error.include-binding-errors=on_param

# ========================================
# CONFIGURATION PAR ENVIRONNEMENT
# ========================================

# Développement
---
spring.config.activate.on-profile=dev
app.error.include-stacktrace=true
server.error.include-exception=true
server.error.include-stacktrace=always
logging.level.bj.douanes.core.shared.error=DEBUG

# Test
---
spring.config.activate.on-profile=test
app.error.include-stacktrace=false
server.error.include-exception=false
server.error.include-stacktrace=never

# Production
---
spring.config.activate.on-profile=prod
app.error.include-stacktrace=false
app.error.include-binding-errors=false
server.error.include-exception=false
server.error.include-stacktrace=never
server.error.include-binding-errors=never

# ========================================
# EXEMPLES D'UTILISATION
# ========================================

# Pour activer ces configurations, ajoutez dans votre application.properties :
# spring.config.import=classpath:application-error-config.properties

# Ou incluez directement les propriétés nécessaires selon votre environnement
