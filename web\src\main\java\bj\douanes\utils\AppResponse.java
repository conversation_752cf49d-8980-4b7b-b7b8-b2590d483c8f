package bj.douanes.utils;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import bj.douanes.core.shared.dto.AppReqResDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public final class AppResponse<T> extends AppReqResDto {
    private static final String SUCCESS = "Request Successfully";
    private static final String CREATED = "Request Created";
    private transient T data;
    private String fileName;

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public AppResponse() {
        super();
        setMessage(SUCCESS);
        setStatusCode(HttpStatus.OK);
    }

    public AppResponse(String message) {
        super();
        setMessage(message);
        setStatusCode(HttpStatus.OK);
    }

    public AppResponse(T data) {
        this();
        setData(data);
    }

    public AppResponse(T data, String message) {
        this(message);
        setData(data);
    }

    public AppResponse(T data, String message, HttpStatus status) {
        this(data, message);
        setStatusCode(status);
    }

    public AppResponse(HttpStatus status) {
        this();
        setStatusCode(status);
    }

    public AppResponse(String message, HttpStatus status) {
        this(status);
        setMessage(message);
    }

    // STATIC Methods
    public static <T> ResponseEntity<AppResponse<T>> ok(T data) {
        return ResponseEntity.ok(new AppResponse<>(data));
    }

    public static <T> ResponseEntity<AppResponse<T>> ok(T data, String message) {
        return ResponseEntity.ok(new AppResponse<>(data, message));
    }

    public static <T> ResponseEntity<AppResponse<T>> of(String message, HttpStatus status) {
        return ResponseEntity.status(status.value()).body(new AppResponse<>(message, status));
    }

    public static <T> ResponseEntity<AppResponse<T>> of(T data, String message, HttpStatus status) {
        return ResponseEntity.status(status.value()).body(new AppResponse<>(data, message, status));
    }

    public static ResponseEntity<AppResponse<Object>> created() {
        return ResponseEntity.status(HttpStatus.CREATED).body(new AppResponse<>(CREATED, HttpStatus.CREATED));
    }

    public static <T> ResponseEntity<AppResponse<T>> created(T data) {
        return ResponseEntity.status(HttpStatus.CREATED).body(new AppResponse<>(data, CREATED, HttpStatus.CREATED));
    }

    public static ResponseEntity<AppResponse<Object>> accepted() {
        return ResponseEntity.status(HttpStatus.ACCEPTED).body(new AppResponse<>(SUCCESS, HttpStatus.ACCEPTED));
    }
    
    //bad request
    public static ResponseEntity<AppResponse<Object>> badRequest(String message) {
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(new AppResponse<>(message, HttpStatus.BAD_REQUEST));
    }

    //okWithFile
    public static ResponseEntity<AppResponse<Object>> okWithFile(byte[] fileData, String fileName) {
        AppResponse<Object> response = new AppResponse<>();
        response.setData(fileData);
        response.setMessage("File downloaded successfully");
        response.setFileName(fileName);
        return ResponseEntity.ok(response);
    }

    // internalServerError
    public static ResponseEntity<AppResponse<Object>> internalServerError(String message) {
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new AppResponse<>(message, HttpStatus.INTERNAL_SERVER_ERROR));
    }
    // noContent
    public static ResponseEntity<AppResponse<Object>> noContent() {
        return ResponseEntity.status(HttpStatus.NO_CONTENT).body(new AppResponse<>(HttpStatus.NO_CONTENT));
    }
}